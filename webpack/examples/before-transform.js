/**
 * 转换前的代码示例
 * 
 * 这个文件展示了 loader 处理前的原始代码状态
 */

// 包含注释的代码
function calculateSum(a, b) {
    // 这是一个单行注释
    var result = a + b;
    
    /*
     * 这是一个多行注释
     * 用于说明函数的作用
     */
    
    return result; // 返回计算结果
}

// 混淆的变量名
var a='\x48\x65\x6c\x6c\x6f';var b='\u0057\u006f\u0072\u006c\u0064';

// 字符串拼接
var message='Hello'+' '+'World';

// 网络请求
fetch('https://api.example.com/data').then(response=>response.json());

// 加密相关代码
var encrypted=CryptoJS.AES.encrypt('secret','password').toString();

// 复杂控制流
function complexFunction(x){
if(x>10){
for(var i=0;i<x;i++){
if(i%2===0){
try{
while(i<5){
switch(i){
case 0:console.log('zero');break;
case 1:console.log('one');break;
default:console.log('other');
}
i++;
}
}catch(e){
console.error(e);
}
}
}
}
return x*2;
}

// 安全问题
var apiKey='sk-1234567890abcdef';
eval('console.log("dynamic code")');
document.getElementById('content').innerHTML='<script>alert("xss")</script>';

console.log('原始代码执行完成');
