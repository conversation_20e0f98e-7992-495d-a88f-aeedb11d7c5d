/**
 * 同步 Webpack Loader 示例
 * 
 * 这个 loader 演示了同步处理的基本原理：
 * 1. 接收源代码作为输入
 * 2. 对代码进行转换处理
 * 3. 返回转换后的代码
 * 
 * 适用场景：
 * - 简单的文本替换
 * - 代码格式化
 * - 添加注释或标记
 * - 基础的代码转换
 */

const loaderUtils = require('loader-utils');

module.exports = function(source) {
  // 获取 loader 配置选项
  const options = loaderUtils.getOptions(this) || {};
  
  // 记录 loader 执行信息
  if (options.logTransform) {
    console.log(`\n🔄 [同步Loader] 正在处理文件: ${this.resourcePath}`);
    console.log(`📝 [同步Loader] 源代码长度: ${source.length} 字符`);
  }
  
  let transformedSource = source;
  
  // 功能1: 移除注释（简单实现）
  if (options.removeComments) {
    // 移除单行注释
    transformedSource = transformedSource.replace(/\/\/.*$/gm, '');
    // 移除多行注释
    transformedSource = transformedSource.replace(/\/\*[\s\S]*?\*\//g, '');
    
    if (options.logTransform) {
      console.log('✅ [同步Loader] 已移除注释');
    }
  }
  
  // 功能2: 添加时间戳标记
  if (options.addTimestamp) {
    const timestamp = new Date().toISOString();
    const timestampComment = `\n/* 🕒 Processed by sync-loader at ${timestamp} */\n`;
    transformedSource = timestampComment + transformedSource;
    
    if (options.logTransform) {
      console.log('✅ [同步Loader] 已添加时间戳');
    }
  }
  
  // 功能3: 代码美化（基础版本）
  transformedSource = beautifyCode(transformedSource);
  
  // 功能4: 添加逆向分析标记
  transformedSource = addReverseAnalysisMarkers(transformedSource);
  
  if (options.logTransform) {
    console.log(`✨ [同步Loader] 处理完成，输出长度: ${transformedSource.length} 字符\n`);
  }
  
  // 返回转换后的代码
  return transformedSource;
};

/**
 * 简单的代码美化函数
 * @param {string} code - 源代码
 * @returns {string} - 美化后的代码
 */
function beautifyCode(code) {
  return code
    // 在操作符前后添加空格
    .replace(/([=+\-*/%<>!&|])/g, ' $1 ')
    // 清理多余的空格
    .replace(/\s+/g, ' ')
    // 在分号后添加换行
    .replace(/;/g, ';\n')
    // 在大括号后添加换行
    .replace(/{/g, '{\n')
    .replace(/}/g, '\n}')
    // 清理多余的换行
    .replace(/\n\s*\n/g, '\n');
}

/**
 * 添加逆向分析相关的标记
 * @param {string} code - 源代码
 * @returns {string} - 添加标记后的代码
 */
function addReverseAnalysisMarkers(code) {
  let markedCode = code;
  
  // 标记可能的加密函数
  const cryptoPatterns = [
    /\b(encrypt|decrypt|cipher|hash|md5|sha|aes|des|rsa)\b/gi,
    /\b(btoa|atob|base64)\b/gi,
    /\b(crypto|CryptoJS)\b/gi
  ];
  
  cryptoPatterns.forEach(pattern => {
    markedCode = markedCode.replace(pattern, '/* 🔐 CRYPTO */ $&');
  });
  
  // 标记网络请求
  const networkPatterns = [
    /\b(fetch|xhr|ajax|request|axios)\b/gi,
    /\b(XMLHttpRequest|fetch)\b/gi
  ];
  
  networkPatterns.forEach(pattern => {
    markedCode = markedCode.replace(pattern, '/* 🌐 NETWORK */ $&');
  });
  
  // 标记可疑的混淆代码
  const obfuscationPatterns = [
    /\b[a-zA-Z_$][a-zA-Z0-9_$]{0,2}\b/g, // 短变量名
    /\\x[0-9a-fA-F]{2}/g, // 十六进制编码
    /\\u[0-9a-fA-F]{4}/g  // Unicode 编码
  ];
  
  // 检测混淆程度
  let obfuscationScore = 0;
  obfuscationPatterns.forEach(pattern => {
    const matches = markedCode.match(pattern);
    if (matches) {
      obfuscationScore += matches.length;
    }
  });
  
  if (obfuscationScore > 10) {
    markedCode = '/* ⚠️  HIGH OBFUSCATION DETECTED */\n' + markedCode;
  }
  
  return markedCode;
}

/**
 * Loader 的缓存标识
 * 告诉 webpack 这个 loader 的结果可以被缓存
 */
module.exports.raw = false; // 处理文本内容，不是二进制
