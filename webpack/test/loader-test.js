/**
 * Webpack Loader 测试文件
 * 
 * 用于测试自定义 loader 的功能
 */

const path = require('path');
const fs = require('fs');

// 导入 loader
const syncLoader = require('../loaders/sync-loader.js');
const asyncLoader = require('../loaders/async-loader.js');
const reverseAnalysisLoader = require('../loaders/reverse-analysis-loader.js');

console.log('🧪 开始 Webpack Loader 测试\n');

// 测试数据
const testCode = `
// 测试注释
function test() {
    var a = '\x48\x65\x6c\x6c\x6f';
    var encrypted = CryptoJS.AES.encrypt('data', 'key');
    fetch('https://api.test.com/data');
    eval('console.log("test")');
}
`;

// 模拟 webpack loader 上下文
function createLoaderContext(resourcePath, options = {}) {
    return {
        resourcePath: resourcePath,
        async: function() {
            return function(err, result) {
                if (err) {
                    console.error('❌ 异步 loader 错误:', err);
                } else {
                    console.log('✅ 异步 loader 完成');
                    console.log('📄 结果预览:', result.substring(0, 200) + '...\n');
                }
            };
        },
        // 模拟 loader-utils.getOptions
        query: options
    };
}

// 测试同步 loader
function testSyncLoader() {
    console.log('🔄 测试同步 Loader...');
    
    const context = createLoaderContext('/test/sync-test.js', {
        removeComments: true,
        addTimestamp: true,
        logTransform: true
    });
    
    // 模拟 loader-utils.getOptions
    const originalGetOptions = require('loader-utils').getOptions;
    require('loader-utils').getOptions = function() {
        return context.query;
    };
    
    try {
        const result = syncLoader.call(context, testCode);
        console.log('✅ 同步 Loader 测试成功');
        console.log('📄 结果预览:', result.substring(0, 300) + '...\n');
    } catch (error) {
        console.error('❌ 同步 Loader 测试失败:', error.message);
    }
    
    // 恢复原始函数
    require('loader-utils').getOptions = originalGetOptions;
}

// 测试异步 loader
function testAsyncLoader() {
    console.log('🔄 测试异步 Loader...');
    
    const context = createLoaderContext('/test/async-test.js', {
        decryptionKey: 'test-key',
        algorithm: 'AES'
    });
    
    // 模拟 loader-utils.getOptions
    const originalGetOptions = require('loader-utils').getOptions;
    require('loader-utils').getOptions = function() {
        return context.query;
    };
    
    try {
        asyncLoader.call(context, 'ENCRYPTED:' + testCode);
        console.log('⏳ 异步 Loader 正在处理...\n');
    } catch (error) {
        console.error('❌ 异步 Loader 测试失败:', error.message);
    }
    
    // 恢复原始函数
    require('loader-utils').getOptions = originalGetOptions;
}

// 测试逆向分析 loader
function testReverseAnalysisLoader() {
    console.log('🔄 测试逆向分析 Loader...');
    
    const obfuscatedCode = `
var a='\x48\x65\x6c\x6c\x6f';
var b='\u0057\u006f\u0072\u006c\u0064';
var c=function(d,e){return d+e;};
var apiKey='sk-1234567890abcdef';
fetch('https://api.example.com/data');
CryptoJS.AES.encrypt('secret','password');
eval('console.log("dynamic")');
setInterval(function(){debugger;},1000);
`;
    
    const context = createLoaderContext('/test/obfuscated.js', {
        deobfuscate: true,
        extractStrings: true,
        analyzeAPIs: true,
        outputAnalysis: true
    });
    
    // 模拟 loader-utils.getOptions
    const originalGetOptions = require('loader-utils').getOptions;
    require('loader-utils').getOptions = function() {
        return context.query;
    };
    
    try {
        const result = reverseAnalysisLoader.call(context, obfuscatedCode);
        console.log('✅ 逆向分析 Loader 测试成功');
        console.log('📄 分析结果预览:', result.substring(0, 500) + '...\n');
    } catch (error) {
        console.error('❌ 逆向分析 Loader 测试失败:', error.message);
    }
    
    // 恢复原始函数
    require('loader-utils').getOptions = originalGetOptions;
}

// 性能测试
function performanceTest() {
    console.log('⚡ 性能测试...');
    
    const largeCode = testCode.repeat(100); // 创建大文件
    const context = createLoaderContext('/test/performance.js', {
        removeComments: true,
        addTimestamp: true,
        logTransform: false // 关闭日志以减少输出
    });
    
    const originalGetOptions = require('loader-utils').getOptions;
    require('loader-utils').getOptions = function() {
        return context.query;
    };
    
    const startTime = Date.now();
    
    try {
        const result = syncLoader.call(context, largeCode);
        const endTime = Date.now();
        const processingTime = endTime - startTime;
        
        console.log(`✅ 性能测试完成`);
        console.log(`📊 处理时间: ${processingTime}ms`);
        console.log(`📏 输入大小: ${largeCode.length} 字符`);
        console.log(`📏 输出大小: ${result.length} 字符`);
        console.log(`⚡ 处理速度: ${Math.round(largeCode.length / processingTime * 1000)} 字符/秒\n`);
    } catch (error) {
        console.error('❌ 性能测试失败:', error.message);
    }
    
    require('loader-utils').getOptions = originalGetOptions;
}

// 运行所有测试
function runAllTests() {
    console.log('🎯 开始运行所有测试...\n');
    
    testSyncLoader();
    
    setTimeout(() => {
        testAsyncLoader();
    }, 1000);
    
    setTimeout(() => {
        testReverseAnalysisLoader();
    }, 2000);
    
    setTimeout(() => {
        performanceTest();
    }, 3000);
    
    setTimeout(() => {
        console.log('🎉 所有测试完成！');
        console.log('\n💡 提示:');
        console.log('- 运行 npm run build 来构建完整项目');
        console.log('- 查看 dist/ 目录下的输出文件');
        console.log('- 对比 examples/ 目录下的转换前后代码');
    }, 4000);
}

// 如果直接运行此文件
if (require.main === module) {
    runAllTests();
}

module.exports = {
    testSyncLoader,
    testAsyncLoader,
    testReverseAnalysisLoader,
    performanceTest,
    runAllTests
};
