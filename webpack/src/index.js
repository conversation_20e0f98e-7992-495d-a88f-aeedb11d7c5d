/**
 * Webpack Loader 演示项目入口文件
 * 
 * 这个文件将被各种 loader 处理，展示不同 loader 的效果
 */

// 导入其他模块进行测试
import './obfuscated.js';
import './encrypted.txt';

console.log('🚀 Webpack Loader 演示项目启动');

// 测试基础功能
function demonstrateBasicFeatures() {
    // 这些注释将被 sync-loader 处理
    console.log('演示基础功能'); // 单行注释
    
    /* 
     * 多行注释
     * 将被移除
     */
    
    const message = 'Hello, Webpack Loaders!';
    return message;
}

// 测试网络请求（将被分析）
async function testNetworkRequests() {
    try {
        // fetch API 调用
        const response = await fetch('https://api.example.com/data');
        const data = await response.json();
        
        // jQuery Ajax 调用示例
        $.ajax({
            url: 'https://api.example.com/users',
            method: 'GET',
            success: function(result) {
                console.log('Ajax 请求成功', result);
            }
        });
        
        // axios 调用示例
        const axiosResponse = await axios.get('https://api.example.com/posts');
        
        return data;
    } catch (error) {
        console.error('网络请求失败:', error);
    }
}

// 测试加密相关功能（将被识别）
function testCryptoFeatures() {
    // CryptoJS 使用示例
    const encrypted = CryptoJS.AES.encrypt('secret message', 'password').toString();
    console.log('加密结果:', encrypted);
    
    // Base64 编码
    const encoded = btoa('Hello World');
    const decoded = atob(encoded);
    
    // MD5 哈希
    const hash = CryptoJS.MD5('test string').toString();
    
    return {
        encrypted,
        encoded,
        decoded,
        hash
    };
}

// 测试复杂控制流（将被分析复杂度）
function complexControlFlow(input) {
    if (input > 100) {
        for (let i = 0; i < input; i++) {
            if (i % 2 === 0) {
                try {
                    while (i < 50) {
                        switch (i % 3) {
                            case 0:
                                console.log('Case 0');
                                break;
                            case 1:
                                console.log('Case 1');
                                break;
                            default:
                                console.log('Default case');
                        }
                        i++;
                    }
                } catch (error) {
                    console.error('Error in complex flow:', error);
                }
            }
        }
    }
    return input * 2;
}

// 包含一些可疑字符串的函数
function suspiciousFunction() {
    const apiKey = 'sk-1234567890abcdef'; // 硬编码的 API 密钥
    const password = 'admin123'; // 硬编码密码
    const secretToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9'; // JWT token
    
    // 使用 eval（安全风险）
    const dynamicCode = 'console.log("Dynamic execution")';
    eval(dynamicCode);
    
    // innerHTML 使用（XSS 风险）
    document.getElementById('content').innerHTML = '<script>alert("XSS")</script>';
    
    return {
        apiKey,
        password,
        secretToken
    };
}

// 主函数
function main() {
    console.log('=== Webpack Loader 演示开始 ===');
    
    // 执行各种测试函数
    demonstrateBasicFeatures();
    testNetworkRequests();
    testCryptoFeatures();
    complexControlFlow(150);
    suspiciousFunction();
    
    console.log('=== 演示完成 ===');
}

// 启动应用
main();

// 导出模块
export {
    demonstrateBasicFeatures,
    testNetworkRequests,
    testCryptoFeatures,
    complexControlFlow,
    suspiciousFunction
};
