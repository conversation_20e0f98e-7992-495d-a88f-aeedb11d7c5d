const path = require('path');

module.exports = {
  // 入口文件
  entry: './src/index.js',
  
  // 输出配置
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'bundle.js',
    clean: true // 每次构建前清理输出目录
  },
  
  // 模块配置
  module: {
    rules: [
      {
        // 处理 .js 文件的同步 loader
        test: /\.js$/,
        exclude: /node_modules/,
        use: [
          {
            loader: path.resolve(__dirname, 'loaders/sync-loader.js'),
            options: {
              // 传递给 loader 的配置选项
              removeComments: true,
              addTimestamp: true,
              logTransform: true
            }
          }
        ]
      },
      {
        // 处理混淆代码的专用 loader
        test: /obfuscated\.js$/,
        use: [
          {
            loader: path.resolve(__dirname, 'loaders/reverse-analysis-loader.js'),
            options: {
              deobfuscate: true,
              extractStrings: true,
              analyzeAPIs: true,
              outputAnalysis: true
            }
          }
        ]
      },
      {
        // 处理加密文件的异步 loader
        test: /encrypted\.(txt|js)$/,
        use: [
          {
            loader: path.resolve(__dirname, 'loaders/async-loader.js'),
            options: {
              decryptionKey: 'demo-key-123',
              algorithm: 'AES',
              outputFormat: 'utf8'
            }
          }
        ]
      }
    ]
  },
  
  // 解析配置
  resolve: {
    extensions: ['.js', '.json'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@loaders': path.resolve(__dirname, 'loaders')
    }
  },
  
  // 开发工具配置
  devtool: 'source-map',
  
  // 模式配置
  mode: 'development',
  
  // 统计信息配置
  stats: {
    colors: true,
    modules: false,
    children: false,
    chunks: false,
    chunkModules: false
  },
  
  // 性能配置
  performance: {
    hints: false
  }
};
