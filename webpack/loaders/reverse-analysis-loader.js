/**
 * 逆向分析专用 Webpack Loader
 * 
 * 这个 loader 专门用于 JavaScript 逆向工程，提供以下功能：
 * 1. 代码去混淆
 * 2. 字符串提取和解码
 * 3. API 调用分析
 * 4. 加密算法识别
 * 5. 控制流分析
 * 
 * 适用场景：
 * - 分析混淆的 JavaScript 代码
 * - 提取加密算法和密钥
 * - 识别关键的 API 调用
 * - 还原代码逻辑结构
 */

const loaderUtils = require('loader-utils');

module.exports = function(source) {
  const options = loaderUtils.getOptions(this) || {};
  
  console.log(`\n🕵️  [逆向分析Loader] 开始分析文件: ${this.resourcePath}`);
  
  let analyzedSource = source;
  const analysisReport = {
    originalSize: source.length,
    transformations: [],
    findings: [],
    recommendations: []
  };
  
  try {
    // 1. 代码去混淆
    if (options.deobfuscate) {
      const deobfuscated = deobfuscateCode(analyzedSource);
      analyzedSource = deobfuscated.code;
      analysisReport.transformations.push('代码去混淆');
      analysisReport.findings.push(...deobfuscated.findings);
    }
    
    // 2. 字符串提取和分析
    if (options.extractStrings) {
      const stringAnalysis = extractAndAnalyzeStrings(analyzedSource);
      analysisReport.findings.push(...stringAnalysis.findings);
      analyzedSource = stringAnalysis.code;
    }
    
    // 3. API 调用分析
    if (options.analyzeAPIs) {
      const apiAnalysis = analyzeAPICalls(analyzedSource);
      analysisReport.findings.push(...apiAnalysis);
    }
    
    // 4. 加密算法识别
    const cryptoAnalysis = identifyCryptoAlgorithms(analyzedSource);
    analysisReport.findings.push(...cryptoAnalysis);
    
    // 5. 控制流分析
    const controlFlowAnalysis = analyzeControlFlow(analyzedSource);
    analysisReport.findings.push(...controlFlowAnalysis);
    
    // 6. 生成分析报告
    if (options.outputAnalysis) {
      analyzedSource = generateAnalysisReport(analyzedSource, analysisReport);
    }
    
    console.log(`✅ [逆向分析Loader] 分析完成，发现 ${analysisReport.findings.length} 个关键点`);
    
  } catch (error) {
    console.error(`❌ [逆向分析Loader] 分析失败:`, error.message);
    // 发生错误时返回原始代码
    return source;
  }
  
  return analyzedSource;
};

/**
 * 代码去混淆
 */
function deobfuscateCode(code) {
  const findings = [];
  let deobfuscatedCode = code;
  
  // 1. 还原十六进制编码的字符串
  const hexMatches = code.match(/\\x[0-9a-fA-F]{2}/g);
  if (hexMatches && hexMatches.length > 0) {
    findings.push({
      type: 'obfuscation',
      category: 'hex-encoding',
      count: hexMatches.length,
      description: `发现 ${hexMatches.length} 个十六进制编码字符串`
    });
    
    deobfuscatedCode = deobfuscatedCode.replace(/\\x([0-9a-fA-F]{2})/g, (match, hex) => {
      return String.fromCharCode(parseInt(hex, 16));
    });
  }
  
  // 2. 还原 Unicode 编码的字符串
  const unicodeMatches = code.match(/\\u[0-9a-fA-F]{4}/g);
  if (unicodeMatches && unicodeMatches.length > 0) {
    findings.push({
      type: 'obfuscation',
      category: 'unicode-encoding',
      count: unicodeMatches.length,
      description: `发现 ${unicodeMatches.length} 个 Unicode 编码字符串`
    });
    
    deobfuscatedCode = deobfuscatedCode.replace(/\\u([0-9a-fA-F]{4})/g, (match, unicode) => {
      return String.fromCharCode(parseInt(unicode, 16));
    });
  }
  
  // 3. 简化复杂的字符串拼接
  deobfuscatedCode = deobfuscatedCode.replace(/['"]([^'"]*)['"]\s*\+\s*['"]([^'"]*)['"]/g, '"$1$2"');
  
  // 4. 还原简单的变量名混淆
  const shortVarPattern = /\b([a-zA-Z_$])(\1*[0-9]*)\b/g;
  const varMap = new Map();
  let varCounter = 0;
  
  deobfuscatedCode = deobfuscatedCode.replace(shortVarPattern, (match) => {
    if (match.length <= 3 && /^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(match)) {
      if (!varMap.has(match)) {
        varMap.set(match, `var_${varCounter++}`);
      }
      return `/* ${match} */ ${varMap.get(match)}`;
    }
    return match;
  });
  
  if (varMap.size > 0) {
    findings.push({
      type: 'obfuscation',
      category: 'variable-names',
      count: varMap.size,
      description: `还原了 ${varMap.size} 个混淆的变量名`
    });
  }
  
  return { code: deobfuscatedCode, findings };
}

/**
 * 提取和分析字符串
 */
function extractAndAnalyzeStrings(code) {
  const findings = [];
  let processedCode = code;
  
  // 提取所有字符串字面量
  const stringPattern = /(['"])((?:(?!\1)[^\\]|\\.)*)(\1)/g;
  const strings = [];
  let match;
  
  while ((match = stringPattern.exec(code)) !== null) {
    strings.push(match[2]);
  }
  
  if (strings.length > 0) {
    // 分析字符串内容
    const urlStrings = strings.filter(s => /^https?:\/\//.test(s));
    const base64Strings = strings.filter(s => /^[A-Za-z0-9+/]+=*$/.test(s) && s.length > 10);
    const suspiciousStrings = strings.filter(s => 
      /password|key|secret|token|api|encrypt|decrypt/i.test(s)
    );
    
    if (urlStrings.length > 0) {
      findings.push({
        type: 'strings',
        category: 'urls',
        items: urlStrings,
        description: `发现 ${urlStrings.length} 个 URL 地址`
      });
    }
    
    if (base64Strings.length > 0) {
      findings.push({
        type: 'strings',
        category: 'base64',
        items: base64Strings.slice(0, 5), // 只显示前5个
        description: `发现 ${base64Strings.length} 个可能的 Base64 编码字符串`
      });
      
      // 尝试解码 Base64 字符串
      base64Strings.forEach((b64, index) => {
        try {
          const decoded = Buffer.from(b64, 'base64').toString('utf8');
          if (decoded.length > 0 && /^[\x20-\x7E]*$/.test(decoded)) {
            processedCode = processedCode.replace(
              new RegExp(`['"]${b64.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`),
              `"${b64}" /* 解码: ${decoded.substring(0, 50)}${decoded.length > 50 ? '...' : ''} */`
            );
          }
        } catch (e) {
          // 解码失败，忽略
        }
      });
    }
    
    if (suspiciousStrings.length > 0) {
      findings.push({
        type: 'strings',
        category: 'suspicious',
        items: suspiciousStrings,
        description: `发现 ${suspiciousStrings.length} 个可疑的敏感字符串`
      });
    }
  }
  
  return { code: processedCode, findings };
}

/**
 * 分析 API 调用
 */
function analyzeAPICalls(code) {
  const findings = [];
  
  // 网络请求 API
  const networkAPIs = [
    { pattern: /fetch\s*\(\s*['"]([^'"]+)['"]/, name: 'fetch' },
    { pattern: /\.ajax\s*\(\s*{[^}]*url\s*:\s*['"]([^'"]+)['"]/, name: 'jQuery.ajax' },
    { pattern: /new\s+XMLHttpRequest\s*\(\s*\)/, name: 'XMLHttpRequest' },
    { pattern: /axios\.(get|post|put|delete)\s*\(\s*['"]([^'"]+)['"]/, name: 'axios' }
  ];
  
  networkAPIs.forEach(api => {
    const matches = [...code.matchAll(new RegExp(api.pattern, 'g'))];
    if (matches.length > 0) {
      findings.push({
        type: 'api-calls',
        category: 'network',
        api: api.name,
        count: matches.length,
        urls: matches.map(m => m[1] || m[2]).filter(Boolean),
        description: `发现 ${matches.length} 个 ${api.name} 网络请求`
      });
    }
  });
  
  // 加密相关 API
  const cryptoAPIs = [
    /CryptoJS\.(AES|DES|TripleDES|RC4|Rabbit|MD5|SHA1|SHA256|SHA512)/g,
    /crypto\.(createHash|createCipher|createDecipher)/g,
    /btoa\s*\(/g,
    /atob\s*\(/g
  ];
  
  cryptoAPIs.forEach((pattern, index) => {
    const matches = [...code.matchAll(pattern)];
    if (matches.length > 0) {
      findings.push({
        type: 'api-calls',
        category: 'crypto',
        count: matches.length,
        methods: matches.map(m => m[0]),
        description: `发现 ${matches.length} 个加密相关的 API 调用`
      });
    }
  });
  
  return findings;
}

/**
 * 识别加密算法
 */
function identifyCryptoAlgorithms(code) {
  const findings = [];
  
  // 常见加密算法特征
  const algorithms = [
    { name: 'AES', patterns: [/\bAES\b/i, /Advanced Encryption Standard/i] },
    { name: 'DES', patterns: [/\bDES\b/i, /Data Encryption Standard/i] },
    { name: 'RSA', patterns: [/\bRSA\b/i, /Rivest.*Shamir.*Adleman/i] },
    { name: 'MD5', patterns: [/\bMD5\b/i, /Message.Digest.5/i] },
    { name: 'SHA', patterns: [/\bSHA[-_]?(1|256|512)?\b/i] },
    { name: 'Base64', patterns: [/\bbase64\b/i, /btoa|atob/] }
  ];
  
  algorithms.forEach(algo => {
    const found = algo.patterns.some(pattern => pattern.test(code));
    if (found) {
      findings.push({
        type: 'crypto-algorithm',
        algorithm: algo.name,
        description: `检测到 ${algo.name} 加密算法的使用`
      });
    }
  });
  
  return findings;
}

/**
 * 控制流分析
 */
function analyzeControlFlow(code) {
  const findings = [];
  
  // 分析函数复杂度
  const functions = code.match(/function\s+\w+\s*\([^)]*\)\s*{[^}]*}/g) || [];
  const complexFunctions = functions.filter(func => {
    const complexity = (func.match(/if|for|while|switch|try/g) || []).length;
    return complexity > 5;
  });
  
  if (complexFunctions.length > 0) {
    findings.push({
      type: 'control-flow',
      category: 'complexity',
      count: complexFunctions.length,
      description: `发现 ${complexFunctions.length} 个高复杂度函数`
    });
  }
  
  // 检测反调试技术
  const antiDebugPatterns = [
    /debugger\s*;/g,
    /console\.(log|debug|info|warn|error)\s*=\s*function/g,
    /setInterval.*debugger/g,
    /Function\s*\(\s*['"]debugger['"]\s*\)/g
  ];
  
  antiDebugPatterns.forEach((pattern, index) => {
    const matches = code.match(pattern);
    if (matches) {
      findings.push({
        type: 'control-flow',
        category: 'anti-debug',
        count: matches.length,
        description: `检测到反调试技术的使用`
      });
    }
  });
  
  return findings;
}

/**
 * 生成分析报告
 */
function generateAnalysisReport(code, report) {
  const reportHeader = `
/*
🕵️  JavaScript 逆向分析报告
================================
📁 文件大小: ${report.originalSize} 字符
🔄 执行的转换: ${report.transformations.join(', ')}

📋 分析发现:
${report.findings.map((finding, index) => `
${index + 1}. ${finding.description}
   类型: ${finding.type}
   ${finding.category ? `分类: ${finding.category}` : ''}
   ${finding.count ? `数量: ${finding.count}` : ''}
   ${finding.items ? `项目: ${finding.items.slice(0, 3).join(', ')}${finding.items.length > 3 ? '...' : ''}` : ''}
`).join('')}

💡 建议:
- 重点关注加密算法的实现细节
- 分析网络请求的参数构造逻辑
- 检查是否存在动态代码执行
- 注意反调试和反分析技术的使用

⚠️  注意: 此分析报告由自动化工具生成，可能存在误报或漏报
*/

`;
  
  return reportHeader + code;
}

module.exports.raw = false;
