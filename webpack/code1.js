/**
 * Webpack Loader 原理演示 - 核心示例文件
 *
 * 这个文件演示了如何创建和使用自定义 webpack loader
 * 适用于 JavaScript 逆向工程和代码分析
 */

console.log('🚀 Webpack Loader 原理演示项目');

// 演示 1: 基础 Loader 概念
console.log('\n=== 演示 1: Loader 基础概念 ===');

/**
 * 最简单的 Loader 示例
 * Loader 本质上就是一个函数，接收源代码，返回转换后的代码
 */
function simpleLoader(source) {
    // 在代码前添加注释
    return `/* 由 simpleLoader 处理 */\n${source}`;
}

// 演示如何手动调用 loader
const originalCode = 'console.log("Hello World");';
const transformedCode = simpleLoader(originalCode);
console.log('原始代码:', originalCode);
console.log('转换后:', transformedCode);

// 演示 2: 带配置选项的 Loader
console.log('\n=== 演示 2: 带配置选项的 Loader ===');

/**
 * 支持配置选项的 Loader
 */
function configurableLoader(source, options = {}) {
    let result = source;

    // 根据配置选项进行不同的转换
    if (options.addTimestamp) {
        const timestamp = new Date().toISOString();
        result = `/* 处理时间: ${timestamp} */\n${result}`;
    }

    if (options.removeComments) {
        // 移除单行注释
        result = result.replace(/\/\/.*$/gm, '');
        // 移除多行注释
        result = result.replace(/\/\*[\s\S]*?\*\//g, '');
    }

    if (options.minify) {
        // 简单的代码压缩
        result = result
            .replace(/\s+/g, ' ')
            .replace(/;\s*}/g, ';}')
            .trim();
    }

    return result;
}

// 测试不同配置
const testCode = `
// 这是注释
function test() {
    /* 多行注释 */
    console.log("测试函数");
}
`;

console.log('原始代码:');
console.log(testCode);

console.log('\n添加时间戳:');
console.log(configurableLoader(testCode, { addTimestamp: true }));

console.log('\n移除注释:');
console.log(configurableLoader(testCode, { removeComments: true }));

console.log('\n压缩代码:');
console.log(configurableLoader(testCode, { minify: true }));

// 演示 3: 逆向工程专用功能
console.log('\n=== 演示 3: 逆向工程应用 ===');

/**
 * 逆向分析专用 Loader 功能演示
 */
function reverseEngineeringDemo() {
    // 混淆代码示例
    const obfuscatedCode = `
var a = '\x48\x65\x6c\x6c\x6f';  // Hello
var b = '\u0057\u006f\u0072\u006c\u0064';  // World
var c = 'SGVsbG8gV29ybGQ=';  // Base64: Hello World
var d = function(e, f) { return e + f; };
fetch('https://api.example.com/data');
CryptoJS.AES.encrypt('secret', 'password');
`;

    console.log('混淆代码分析:');
    console.log('原始混淆代码:');
    console.log(obfuscatedCode);

    // 解码十六进制字符串
    const hexDecoded = obfuscatedCode.replace(/\\x([0-9a-fA-F]{2})/g, (match, hex) => {
        const char = String.fromCharCode(parseInt(hex, 16));
        return `${match} /* '${char}' */`;
    });

    // 解码 Unicode 字符串
    const unicodeDecoded = hexDecoded.replace(/\\u([0-9a-fA-F]{4})/g, (match, unicode) => {
        const char = String.fromCharCode(parseInt(unicode, 16));
        return `${match} /* '${char}' */`;
    });

    // 标记加密相关代码
    const cryptoMarked = unicodeDecoded.replace(/(CryptoJS|AES|encrypt|decrypt)/g, '🔐 $1');

    // 标记网络请求
    const networkMarked = cryptoMarked.replace(/(fetch|ajax|XMLHttpRequest)/g, '🌐 $1');

    console.log('\n分析后的代码:');
    console.log(networkMarked);

    // 提取关键信息
    const urls = obfuscatedCode.match(/https?:\/\/[^\s'"]+/g) || [];
    const cryptoMethods = obfuscatedCode.match(/CryptoJS\.\w+/g) || [];

    console.log('\n提取的关键信息:');
    console.log('🌐 发现的 URL:', urls);
    console.log('🔐 加密方法:', cryptoMethods);
}

reverseEngineeringDemo();

// 演示 4: Webpack 配置示例
console.log('\n=== 演示 4: Webpack 配置示例 ===');

/**
 * 展示如何在 webpack.config.js 中配置自定义 loader
 */
const webpackConfigExample = {
    module: {
        rules: [
            {
                test: /\.js$/,
                use: [
                    {
                        loader: 'path/to/custom-loader.js',
                        options: {
                            removeComments: true,
                            addTimestamp: true,
                            analyzeCode: true
                        }
                    }
                ]
            },
            {
                test: /obfuscated\.js$/,
                use: [
                    {
                        loader: 'path/to/reverse-analysis-loader.js',
                        options: {
                            deobfuscate: true,
                            extractStrings: true,
                            identifyCrypto: true
                        }
                    }
                ]
            }
        ]
    }
};

console.log('Webpack 配置示例:');
console.log(JSON.stringify(webpackConfigExample, null, 2));

// 演示 5: 实际应用场景
console.log('\n=== 演示 5: 实际应用场景 ===');

console.log(`
🎯 Webpack Loader 在逆向工程中的应用场景:

1. 📝 代码去混淆
   - 还原变量名和函数名
   - 解码字符串编码
   - 简化复杂表达式

2. 🔍 代码分析
   - 提取 API 端点
   - 识别加密算法
   - 分析数据流

3. 🛡️  安全审计
   - 检测危险函数调用
   - 识别硬编码密钥
   - 发现潜在漏洞

4. 📊 统计分析
   - 代码复杂度分析
   - 依赖关系图
   - 性能瓶颈识别

5. 🔧 自动化处理
   - 批量代码转换
   - 格式化和美化
   - 添加分析注释
`);

console.log('\n✨ 演示完成！查看项目文件了解更多详细实现。');