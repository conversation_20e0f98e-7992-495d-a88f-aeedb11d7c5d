# Webpack Loader 原理演示项目

## 项目概述

本项目演示了 webpack loader 的核心工作原理，特别适用于 JavaScript 逆向工程中的代码分析和转换场景。

## 项目结构

```
webpack/
├── README.md                    # 项目说明文档
├── package.json                 # 项目依赖配置
├── webpack.config.js            # webpack 配置文件
├── loaders/                     # 自定义 loader 目录
│   ├── sync-loader.js          # 同步 loader 示例
│   ├── async-loader.js         # 异步 loader 示例
│   └── reverse-analysis-loader.js # 逆向分析专用 loader
├── src/                        # 源文件目录
│   ├── index.js                # 入口文件
│   ├── obfuscated.js           # 混淆代码示例
│   └── encrypted.txt           # 加密文件示例
├── dist/                       # 构建输出目录
└── examples/                   # 示例和测试文件
    ├── before-transform.js     # 转换前的代码
    └── after-transform.js      # 转换后的代码
```

## Webpack Loader 核心概念

### 1. Loader 是什么？

Loader 是 webpack 的核心功能之一，它允许你预处理文件。Loader 可以将文件从不同的语言（如 TypeScript）转换为 JavaScript，或将内联图像转换为 data URL。

### 2. Loader 工作原理

- **链式调用**：多个 loader 可以链式调用，从右到左执行
- **单一职责**：每个 loader 只做一件事情
- **可配置**：通过 options 参数接收配置
- **异步支持**：支持同步和异步处理

### 3. 在逆向工程中的应用

在 JavaScript 逆向分析中，loader 可以用于：

1. **代码去混淆**：自动识别和还原混淆的变量名和函数名
2. **加密解密**：处理加密的 JavaScript 代码
3. **代码格式化**：美化压缩的代码，提高可读性
4. **依赖分析**：分析模块间的依赖关系
5. **API 调用追踪**：识别和标记关键的 API 调用

## 快速开始

### 1. 安装依赖

```bash
cd webpack
npm install
```

### 2. 运行构建

```bash
npm run build
```

### 3. 查看结果

构建完成后，查看 `dist/` 目录下的输出文件，对比 `examples/` 目录下的转换前后代码。

## Loader 开发指南

### 同步 Loader

```javascript
module.exports = function(source) {
  // 处理源代码
  const transformedSource = transform(source);
  return transformedSource;
};
```

### 异步 Loader

```javascript
module.exports = function(source) {
  const callback = this.async();
  
  // 异步处理
  processAsync(source, (err, result) => {
    if (err) return callback(err);
    callback(null, result);
  });
};
```

### 获取 Loader 选项

```javascript
const loaderUtils = require('loader-utils');

module.exports = function(source) {
  const options = loaderUtils.getOptions(this) || {};
  // 使用配置选项处理代码
};
```

## 调试技巧

1. **使用 console.log**：在 loader 中添加日志输出
2. **Source Map 支持**：生成 source map 便于调试
3. **单元测试**：为 loader 编写测试用例
4. **Webpack Stats**：分析构建统计信息

## 实际应用场景

### 场景1：自动去混淆

当遇到混淆的 JavaScript 代码时，可以编写专门的 loader 来：
- 还原变量名
- 简化复杂的表达式
- 移除无用的代码

### 场景2：加密代码处理

对于加密的 JavaScript 文件：
- 自动解密
- 提取关键信息
- 生成可读的代码

### 场景3：API 分析

分析目标网站的 JavaScript 代码：
- 识别加密算法
- 提取 API 端点
- 分析请求参数

## 注意事项

1. **性能考虑**：避免在 loader 中进行重复的计算
2. **错误处理**：妥善处理异常情况
3. **缓存机制**：利用 webpack 的缓存提高构建速度
4. **兼容性**：确保 loader 与不同版本的 webpack 兼容

## 快速开始演示

### 方式一：使用启动脚本（推荐）

```bash
# 进入项目目录
cd webpack

# 运行完整演示
node start-demo.js all

# 或者分步运行
node start-demo.js basic    # 基础演示
node start-demo.js test     # Loader 测试
node start-demo.js build    # 构建项目
node start-demo.js compare  # 查看对比
```

### 方式二：手动运行

```bash
# 1. 安装依赖
npm install

# 2. 运行基础演示
node code1.js

# 3. 测试 loader
npm test

# 4. 构建项目
npm run build

# 5. 查看结果
ls -la dist/
```

## 项目特色

### 🎯 专为逆向工程设计
- **代码去混淆**：自动识别和还原常见的混淆技术
- **加密算法识别**：检测和标记各种加密算法的使用
- **API 调用分析**：提取和分析网络请求和关键 API 调用
- **安全审计**：发现潜在的安全问题和漏洞

### 🔧 完整的 Loader 生态
- **同步 Loader**：展示基础的同步处理模式
- **异步 Loader**：演示复杂的异步操作处理
- **专业分析 Loader**：专门用于逆向工程的高级功能

### 📚 丰富的学习资源
- **详细注释**：每个文件都有完整的中文注释
- **对比示例**：提供转换前后的代码对比
- **测试用例**：包含完整的测试和验证代码
- **实际应用**：展示在真实逆向场景中的应用

## 扩展阅读

- [Webpack Loader 官方文档](https://webpack.js.org/concepts/loaders/)
- [编写自定义 Loader](https://webpack.js.org/contribute/writing-a-loader/)
- [Loader API 参考](https://webpack.js.org/api/loaders/)
- [JavaScript 逆向工程实践](https://github.com/topics/javascript-reverse-engineering)
