#!/usr/bin/env node

/**
 * Webpack Loader 演示项目启动脚本
 * 
 * 这个脚本提供了一个交互式的演示环境
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log(`
🎯 Webpack Loader 原理演示项目
================================

这个项目演示了 webpack loader 的核心工作原理，
特别适用于 JavaScript 逆向工程和代码分析。

📁 项目结构:
├── loaders/           # 自定义 loader 实现
├── src/              # 源文件和测试用例
├── examples/         # 转换前后对比示例
├── test/             # 测试文件
└── dist/             # 构建输出目录

🔧 可用的 Loader:
1. sync-loader.js           - 同步 loader 示例
2. async-loader.js          - 异步 loader 示例  
3. reverse-analysis-loader.js - 逆向分析专用 loader

`);

// 检查依赖
function checkDependencies() {
    console.log('🔍 检查项目依赖...');
    
    if (!fs.existsSync('node_modules')) {
        console.log('📦 安装依赖包...');
        try {
            execSync('npm install', { stdio: 'inherit' });
            console.log('✅ 依赖安装完成');
        } catch (error) {
            console.error('❌ 依赖安装失败:', error.message);
            process.exit(1);
        }
    } else {
        console.log('✅ 依赖已存在');
    }
}

// 运行基础演示
function runBasicDemo() {
    console.log('\n🚀 运行基础演示...');
    try {
        execSync('node code1.js', { stdio: 'inherit' });
    } catch (error) {
        console.error('❌ 基础演示运行失败:', error.message);
    }
}

// 运行 loader 测试
function runLoaderTests() {
    console.log('\n🧪 运行 Loader 测试...');
    try {
        execSync('node test/loader-test.js', { stdio: 'inherit' });
    } catch (error) {
        console.error('❌ Loader 测试失败:', error.message);
    }
}

// 构建项目
function buildProject() {
    console.log('\n🔨 构建项目...');
    try {
        execSync('npm run build', { stdio: 'inherit' });
        console.log('✅ 项目构建完成');
        
        // 显示构建结果
        if (fs.existsSync('dist')) {
            const files = fs.readdirSync('dist');
            console.log('\n📁 构建输出文件:');
            files.forEach(file => {
                const filePath = path.join('dist', file);
                const stats = fs.statSync(filePath);
                console.log(`   ${file} (${Math.round(stats.size / 1024)}KB)`);
            });
        }
    } catch (error) {
        console.error('❌ 项目构建失败:', error.message);
    }
}

// 显示文件对比
function showComparison() {
    console.log('\n📊 查看转换前后对比...');
    
    const beforeFile = 'examples/before-transform.js';
    const afterFile = 'examples/after-transform.js';
    
    if (fs.existsSync(beforeFile) && fs.existsSync(afterFile)) {
        console.log('\n--- 转换前 (before-transform.js) ---');
        const beforeContent = fs.readFileSync(beforeFile, 'utf8');
        console.log(beforeContent.substring(0, 500) + '...');
        
        console.log('\n--- 转换后 (after-transform.js) ---');
        const afterContent = fs.readFileSync(afterFile, 'utf8');
        console.log(afterContent.substring(0, 500) + '...');
        
        console.log('\n💡 完整对比请查看 examples/ 目录下的文件');
    } else {
        console.log('❌ 对比文件不存在');
    }
}

// 显示帮助信息
function showHelp() {
    console.log(`
🎯 可用命令:

1. node start-demo.js basic     - 运行基础演示
2. node start-demo.js test      - 运行 loader 测试
3. node start-demo.js build     - 构建项目
4. node start-demo.js compare   - 查看转换对比
5. node start-demo.js all       - 运行完整演示
6. node start-demo.js help      - 显示帮助信息

📚 学习资源:
- README.md           - 详细文档
- loaders/            - Loader 实现代码
- examples/           - 转换前后对比
- webpack.config.js   - Webpack 配置示例

🔗 相关链接:
- Webpack Loader 官方文档: https://webpack.js.org/concepts/loaders/
- 编写自定义 Loader: https://webpack.js.org/contribute/writing-a-loader/
`);
}

// 运行完整演示
function runFullDemo() {
    console.log('🎬 开始完整演示...\n');
    
    checkDependencies();
    runBasicDemo();
    
    setTimeout(() => {
        runLoaderTests();
    }, 2000);
    
    setTimeout(() => {
        buildProject();
    }, 5000);
    
    setTimeout(() => {
        showComparison();
    }, 8000);
    
    setTimeout(() => {
        console.log('\n🎉 完整演示结束！');
        console.log('\n💡 接下来你可以:');
        console.log('- 修改 src/ 目录下的文件，然后重新构建');
        console.log('- 编写自己的 loader 并添加到 loaders/ 目录');
        console.log('- 在 webpack.config.js 中配置新的 loader 规则');
        console.log('- 查看 dist/ 目录下的构建结果');
    }, 10000);
}

// 主函数
function main() {
    const command = process.argv[2] || 'help';
    
    switch (command) {
        case 'basic':
            checkDependencies();
            runBasicDemo();
            break;
        case 'test':
            checkDependencies();
            runLoaderTests();
            break;
        case 'build':
            checkDependencies();
            buildProject();
            break;
        case 'compare':
            showComparison();
            break;
        case 'all':
            runFullDemo();
            break;
        case 'help':
        default:
            showHelp();
            break;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    checkDependencies,
    runBasicDemo,
    runLoaderTests,
    buildProject,
    showComparison,
    runFullDemo
};
