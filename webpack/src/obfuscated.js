/**
 * 混淆代码示例文件
 * 
 * 这个文件包含各种混淆技术，用于测试逆向分析 loader 的能力
 */

// 十六进制编码的字符串
var a = '\x48\x65\x6c\x6c\x6f\x20\x57\x6f\x72\x6c\x64';

// Unicode 编码的字符串
var b = '\u0048\u0065\u006c\u006c\u006f\u0020\u0057\u006f\u0072\u006c\u0064';

// 短变量名混淆
var c = function(d, e) {
    var f = d + e;
    var g = f * 2;
    return g;
};

// 字符串拼接混淆
var h = 'Hello' + ' ' + 'World';
var i = 'api' + '.' + 'example' + '.' + 'com';

// Base64 编码的数据
var j = 'SGVsbG8gV29ybGQ='; // "Hello World" in base64
var k = 'YWRtaW4xMjM='; // "admin123" in base64

// 复杂的函数名混淆
var _0x1a2b = function(_0x3c4d, _0x5e6f) {
    var _0x7890 = _0x3c4d;
    var _0xabcd = _0x5e6f;
    return _0x7890 + _0xabcd;
};

// 数组混淆
var _0xarray = ['password', 'secret', 'token', 'key'];
var _0xindex = 0;

// 反调试代码
setInterval(function() {
    debugger;
}, 1000);

// 动态函数构造
var _0xfunc = Function('\x64\x65\x62\x75\x67\x67\x65\x72');

// 加密算法相关的混淆代码
var _0xcrypto = {
    '\x65\x6e\x63\x72\x79\x70\x74': function(data, key) {
        // 模拟 AES 加密
        return CryptoJS.AES.encrypt(data, key).toString();
    },
    '\x64\x65\x63\x72\x79\x70\x74': function(encrypted, key) {
        // 模拟 AES 解密
        var bytes = CryptoJS.AES.decrypt(encrypted, key);
        return bytes.toString(CryptoJS.enc.Utf8);
    }
};

// 网络请求混淆
var _0xurl = '\x68\x74\x74\x70\x73\x3a\x2f\x2f\x61\x70\x69\x2e\x65\x78\x61\x6d\x70\x6c\x65\x2e\x63\x6f\x6d';

function _0xrequest() {
    fetch(_0xurl + '/data')
        .then(response => response.json())
        .then(data => {
            console.log(data);
        });
}

// 控制台劫持
console.log = function() {};
console.debug = function() {};
console.info = function() {};

// 复杂的字符串解码函数
function _0xdecode(_0xstr) {
    var _0xresult = '';
    for (var _0xi = 0; _0xi < _0xstr.length; _0xi += 2) {
        _0xresult += String.fromCharCode(parseInt(_0xstr.substr(_0xi, 2), 16));
    }
    return _0xresult;
}

// 使用解码函数
var _0xencoded = '48656c6c6f20576f726c64';
var _0xdecoded = _0xdecode(_0xencoded);

// 混淆的 API 密钥
var _0xapikey = _0xdecode('736b2d31323334353637383930616263646566');

// 导出混淆的函数
window._0xobfuscated = {
    init: function() {
        _0xrequest();
        console.log(_0xdecoded);
    },
    encrypt: _0xcrypto['\x65\x6e\x63\x72\x79\x70\x74'],
    decrypt: _0xcrypto['\x64\x65\x63\x72\x79\x70\x74']
};

// 立即执行函数表达式 (IIFE) 混淆
(function(_0xwindow, _0xdocument) {
    'use strict';
    
    var _0xconfig = {
        '\x61\x70\x69\x55\x72\x6c': _0xurl,
        '\x61\x70\x69\x4b\x65\x79': _0xapikey,
        '\x64\x65\x62\x75\x67': false
    };
    
    if (!_0xconfig['\x64\x65\x62\x75\x67']) {
        _0xfunc();
    }
    
})(window, document);
