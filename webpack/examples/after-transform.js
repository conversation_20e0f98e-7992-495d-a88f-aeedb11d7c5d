/* 🕒 Processed by sync-loader at 2024-01-15T10:30:00.000Z */

/**
 * 转换后的代码示例
 * 
 * 这个文件展示了经过各种 loader 处理后的代码状态
 */

function calculateSum(a, b) {
    
    var result  =  a  +  b;
    
    
    
    
    
    
    return result;
    
}


var /* a */ var_0 = 'Hello';
var /* b */ var_1 = 'World';


var message = "Hello World";


/* 🌐 NETWORK */ fetch('https://api.example.com/data').then(response = >response.json());


var encrypted = /* 🔐 CRYPTO */ CryptoJS./* 🔐 CRYPTO */ AES./* 🔐 CRYPTO */ encrypt('secret','password').toString();


function complexFunction(x){
if(x > 10){
for(var i = 0;
i < x;
i++){
if(i % 2 === 0){
try{
while(i < 5){
switch(i){
case 0:
console.log('zero');
break;

case 1:
console.log('one');
break;

default:
console.log('other');

}
i++;

}
}
catch(e){
console.error(e);

}
}
}
}
return x * 2;

}


var apiKey = 'sk-1234567890abcdef';
 /* ⚠️  SECURITY ISSUE: eval() detected */
eval('console.log("dynamic code")');
 /* ⚠️  SECURITY ISSUE: innerHTML usage detected */
document.getElementById('content').innerHTML = '<script>alert("xss")</script>';

console.log('处理后的代码执行完成');


/*
🔍 代码分析报告
===================
📊 基础统计:
   - 行数: 45
   - 字符数: 1250
   
🔧 函数分析:
   - 函数数量: 2
   - 函数列表: calculateSum, complexFunction
   
🔒 安全分析:
   - 发现问题: 3 个
   - 硬编码密钥 (high)
   - eval() 调用 (high)
   - innerHTML 使用 (medium)
   
🌐 API 调用:
   - 发现 1 个 API 调用
   - https://api.example.com/data
   
🔐 加密算法:
   - 检测到 AES 加密算法的使用
   
⚠️  混淆程度: medium
   - 发现 15 个十六进制编码字符串
   - 发现 8 个 Unicode 编码字符串
   - 还原了 5 个混淆的变量名
*/
