ENCRYPTED:U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y96Qsv2Lm+31cmzaAILwytX/z66ZVWEQM/ccf1g+9m5Ubu1+sit+A9cenDxxqklaxbm4cPSe8KLpZxNTdqbI0=

// 这是一个模拟的加密文件
// 实际内容: console.log('这是解密后的内容');

ENCRYPTED:SGVsbG8gV29ybGQ=

// Base64 编码的内容
// 实际内容: Hello World

ENCRYPTED:function secretFunction() {
    var key = 'demo-secret-key';
    var data = 'sensitive information';
    return CryptoJS.AES.encrypt(data, key).toString();
}
