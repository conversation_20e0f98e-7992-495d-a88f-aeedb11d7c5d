{"name": "webpack-loader-demo", "version": "1.0.0", "description": "Webpack Loader 原理演示项目 - 适用于 JavaScript 逆向工程", "main": "src/index.js", "scripts": {"build": "webpack --mode development", "build:prod": "webpack --mode production", "dev": "webpack --mode development --watch", "clean": "rm -rf dist/*", "test": "node test/loader-test.js"}, "keywords": ["webpack", "loader", "javascript", "reverse-engineering", "deobfuscation", "code-analysis"], "author": "逆向分析师", "license": "MIT", "devDependencies": {"webpack": "^5.88.0", "webpack-cli": "^5.1.0", "loader-utils": "^3.2.1"}, "dependencies": {"crypto-js": "^4.2.0"}}