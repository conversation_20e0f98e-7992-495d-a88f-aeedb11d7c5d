/**
 * 异步 Webpack Loader 示例
 * 
 * 这个 loader 演示了异步处理的原理：
 * 1. 使用 this.async() 获取回调函数
 * 2. 执行异步操作（如文件读取、网络请求、解密等）
 * 3. 通过回调函数返回结果
 * 
 * 适用场景：
 * - 文件解密
 * - 网络请求获取数据
 * - 复杂的代码分析
 * - 数据库查询
 */

const loaderUtils = require('loader-utils');
const crypto = require('crypto');

module.exports = function(source) {
  // 获取异步回调函数
  const callback = this.async();
  
  // 获取配置选项
  const options = loaderUtils.getOptions(this) || {};
  
  console.log(`\n🔄 [异步Loader] 开始处理文件: ${this.resourcePath}`);
  console.log(`⏳ [异步Loader] 模拟异步操作...`);
  
  // 模拟异步操作
  setTimeout(() => {
    try {
      let result = source;
      
      // 功能1: 解密处理
      if (options.decryptionKey) {
        result = decryptContent(result, options);
      }
      
      // 功能2: 异步代码分析
      analyzeCodeAsync(result, (analysisResult) => {
        // 功能3: 添加分析结果到代码中
        const finalResult = addAnalysisResults(result, analysisResult);
        
        console.log(`✅ [异步Loader] 处理完成`);
        console.log(`📊 [异步Loader] 分析结果:`, analysisResult);
        
        // 通过回调返回结果
        // callback(error, result, sourceMap, meta)
        callback(null, finalResult);
      });
      
    } catch (error) {
      console.error(`❌ [异步Loader] 处理失败:`, error.message);
      callback(error);
    }
  }, 1000); // 模拟1秒的异步操作
};

/**
 * 解密内容
 * @param {string} content - 加密的内容
 * @param {object} options - 配置选项
 * @returns {string} - 解密后的内容
 */
function decryptContent(content, options) {
  const { decryptionKey, algorithm = 'AES' } = options;
  
  console.log(`🔓 [异步Loader] 尝试解密，算法: ${algorithm}`);
  
  try {
    // 检查是否是加密内容（简单判断）
    if (content.includes('ENCRYPTED:') || content.includes('U2FsdGVkX1')) {
      // 模拟解密过程
      const decrypted = content
        .replace(/ENCRYPTED:/g, '// 已解密: ')
        .replace(/U2FsdGVkX1[A-Za-z0-9+/=]+/g, 'decryptedContent');
      
      console.log(`✅ [异步Loader] 解密成功`);
      return `/* 🔓 解密密钥: ${decryptionKey} */\n${decrypted}`;
    }
    
    // 如果不是加密内容，直接返回
    return content;
    
  } catch (error) {
    console.warn(`⚠️  [异步Loader] 解密失败，返回原内容:`, error.message);
    return content;
  }
}

/**
 * 异步代码分析
 * @param {string} code - 要分析的代码
 * @param {function} callback - 分析完成后的回调
 */
function analyzeCodeAsync(code, callback) {
  console.log(`🔍 [异步Loader] 开始代码分析...`);
  
  // 模拟异步分析过程
  setTimeout(() => {
    const analysis = {
      // 基础统计
      lineCount: code.split('\n').length,
      charCount: code.length,
      
      // 函数分析
      functions: extractFunctions(code),
      
      // 变量分析
      variables: extractVariables(code),
      
      // 安全分析
      securityIssues: findSecurityIssues(code),
      
      // API 调用分析
      apiCalls: findAPICalls(code),
      
      // 混淆检测
      obfuscationLevel: detectObfuscation(code)
    };
    
    callback(analysis);
  }, 500);
}

/**
 * 提取函数信息
 */
function extractFunctions(code) {
  const functionPattern = /function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/g;
  const arrowFunctionPattern = /(?:const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*\(/g;
  
  const functions = [];
  let match;
  
  while ((match = functionPattern.exec(code)) !== null) {
    functions.push({ name: match[1], type: 'function' });
  }
  
  while ((match = arrowFunctionPattern.exec(code)) !== null) {
    functions.push({ name: match[1], type: 'arrow' });
  }
  
  return functions;
}

/**
 * 提取变量信息
 */
function extractVariables(code) {
  const varPattern = /(?:const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g;
  const variables = [];
  let match;
  
  while ((match = varPattern.exec(code)) !== null) {
    variables.push(match[1]);
  }
  
  return [...new Set(variables)]; // 去重
}

/**
 * 查找安全问题
 */
function findSecurityIssues(code) {
  const issues = [];
  
  // 检查 eval 使用
  if (code.includes('eval(')) {
    issues.push({ type: 'eval', severity: 'high', message: '发现 eval() 调用' });
  }
  
  // 检查 innerHTML 使用
  if (code.includes('innerHTML')) {
    issues.push({ type: 'xss', severity: 'medium', message: '发现 innerHTML 使用' });
  }
  
  // 检查硬编码密钥
  const keyPattern = /(password|key|secret|token)\s*[:=]\s*['"][^'"]+['"]/gi;
  if (keyPattern.test(code)) {
    issues.push({ type: 'hardcoded-secret', severity: 'high', message: '发现硬编码密钥' });
  }
  
  return issues;
}

/**
 * 查找 API 调用
 */
function findAPICalls(code) {
  const apiPatterns = [
    /fetch\s*\(\s*['"]([^'"]+)['"]/g,
    /\.ajax\s*\(\s*{[^}]*url\s*:\s*['"]([^'"]+)['"]/g,
    /XMLHttpRequest.*open\s*\(\s*['"][^'"]*['"],\s*['"]([^'"]+)['"]/g
  ];
  
  const apis = [];
  
  apiPatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(code)) !== null) {
      apis.push(match[1]);
    }
  });
  
  return [...new Set(apis)];
}

/**
 * 检测混淆程度
 */
function detectObfuscation(code) {
  let score = 0;
  
  // 短变量名
  const shortVars = code.match(/\b[a-zA-Z_$]{1,2}\b/g);
  if (shortVars) score += shortVars.length * 0.1;
  
  // 十六进制字符串
  const hexStrings = code.match(/\\x[0-9a-fA-F]{2}/g);
  if (hexStrings) score += hexStrings.length * 2;
  
  // Unicode 编码
  const unicodeStrings = code.match(/\\u[0-9a-fA-F]{4}/g);
  if (unicodeStrings) score += unicodeStrings.length * 2;
  
  // 返回混淆等级
  if (score > 50) return 'high';
  if (score > 20) return 'medium';
  if (score > 5) return 'low';
  return 'none';
}

/**
 * 将分析结果添加到代码中
 */
function addAnalysisResults(code, analysis) {
  const analysisComment = `
/*
🔍 异步代码分析报告
===================
📊 基础统计:
   - 行数: ${analysis.lineCount}
   - 字符数: ${analysis.charCount}
   
🔧 函数分析:
   - 函数数量: ${analysis.functions.length}
   - 函数列表: ${analysis.functions.map(f => f.name).join(', ')}
   
🔒 安全分析:
   - 发现问题: ${analysis.securityIssues.length} 个
   ${analysis.securityIssues.map(issue => `   - ${issue.message} (${issue.severity})`).join('\n')}
   
🌐 API 调用:
   - 发现 ${analysis.apiCalls.length} 个 API 调用
   ${analysis.apiCalls.map(api => `   - ${api}`).join('\n')}
   
⚠️  混淆程度: ${analysis.obfuscationLevel}
*/
`;
  
  return analysisComment + '\n' + code;
}

// 标记这个 loader 处理的是文本内容
module.exports.raw = false;
